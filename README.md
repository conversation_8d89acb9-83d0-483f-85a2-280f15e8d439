# hk-ifp-uw-core-service
basePath: 

## Description
Sample service to demonstrate RSF Kubernetes capabilities.

## Non-Functional Requirements (NFR)
- Latency: <50 ms
- Thruput: 5000 requests per second
- Apdex: 1
- Memory: < 200 MB
- CPU: 200 ci
- Load Time: < 1.5 seconds
- Concurrency: 5000 calls per second
- Scaling: CPU & Memory based

## Build, Test and Deploy

**Prepare**

```	
cd <project>
git init
git add --all
git commit -m "Initial Commit"
git remote add origin <location/project>.git
git push -u origin master
```
**Build**	

Step 1: Verify PaaS target
RSF provides you option to use Kubernetes or Pivotal technologies for service discovery

- To use Pivotal based technology stack, use following dependency
```aidl
    <!-- RSF PIVOTAL -->
    <dependency>
        <groupId>com.manulife.ap</groupId>
        <artifactId>rsf-pivotal</artifactId>
        <version>1.0.0</version>
        <type>pom</type>
    </dependency>
```

- To use K8S based technology stack, use following dependency
```aidl
    <!-- RSF Kubernetes -->
    <dependency>
        <groupId>com.manulife.ap</groupId>
        <artifactId>rsf-k8s-parent</artifactId>
        <version>1.0.0</version>
        <type>pom</type>
        <scope>provided</scope>
    </dependency>
```
rsf-k8s-parent should NOT be used when deploying to PCF. 

Step 2: Compile

```
mvnw clean install
```

Step 3: Test locally

```
mvnw spring-boot:run -Dspring.profiles.active=dev
```

** httpie calls on hk-ifp-uw-core-service service **


Get Price for product

```
http --verbose -a test:test123 GET http://localhost:8082/pricer/product/5555
```

```
http --verbose  GET https://yoda.manulife.com/pricer/product/myprod122 Ocp-Apim-Subscription-Key:25a5222311294a4b8431dbe2a7265a83
```

### Cloud Deployment

#### PCF

**Deploy PCF**

```
cf push -f manifest-dev.yml
```

#### Kubernetes
**Build docker image**
```
docker build -f cicd/docker/Dockerfile -t hk-ifp-uw-core-service:0.0.2-SNAPSHOT .
```


**Run docker locally**

```
./cicd/docker/runDocker.sh
```

**Helm Deployment**

(To Review)

```
helm install hk-ifp-uw-core-service helm/hk-ifp-uw-core-service
```

**Manual Deployment**

Step 1: Tag ACR

```
docker tag hk-ifp-uw-core-service:0.0.2-SNAPSHOT artifactory.ap.manulife.com/docker/hk-ifp-uw-core-service:0.0.2-SNAPSHOT
```

Step 2: Push to ACR

```
docker push artifactory.ap.manulife.com/docker/hk-ifp-uw-core-service:0.0.2-SNAPSHOT
```

Step 3: Create K8S deployment YAML

Refer to ..\cicd\k8s\configs\deploy.yaml

**Run in AKS**

```
kubectl apply -f deploy.yaml
```

Access as Service (if not wanting to add Service as config)

```
kubectl expose deployment hk-ifp-uw-core-service-deployment --type=NodePort --name=hk-ifp-uw-core-service-service
```

**Validate**

```
| => kubectl get pods,deployments,services | grep hk-ifp-uw-core-service
pod/dep-hk-ifp-uw-core-service-6fbbbdcb47-s8dfd   1/1     Running   0          81m
deployment.extensions/dep-hk-ifp-uw-core-service   1/1     1            1           81m
```
**Test with AKS**

```
kubectl port-forward <podid> 8082:8080
```

**Functional Test using POSTMAN**

Open POSTMAN to execute API commands

OR

Test with httpie commands above


**Performance Test using fortio**

```
fortio load -a -c 10 -qps 50 -t 5m http://localhost:8082/pricer/product/productid
```

```
fortio load -a -c 8 -qps 500 -t 10m -H Ocp-Apim-Subscription-Key:25a5222311294a4b8431dbe2a7265a83 https://yoda.manulife.com/pricer/product/myprod122
```
**Monitor**

**Helm Chart**


helm install hk-ifp-uw-core-service-1.0.1-1.0.0 helm/hk-ifp-uw-core-service-1.0.1 --version 1.0.0 --set image.repository=artifactory.ap.manulife.com/docker --set appName=hk-ifp-uw-core-service --set appVersion=1.0.1 --values cicd/k8s/values/values-dev.yaml --timeout 10m
Helm chart is run off RSF Helm template (Yet to be released). This will be part of ATO DevOps pipeline
