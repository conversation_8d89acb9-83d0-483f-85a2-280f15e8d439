/*************************************************************************
 * Manulife Financial Service CONFIDENTIAL
 * 
 *  All Rights Reserved.
 * 
 * NOTICE:  All information contained herein is, and remains
 * the property of Manulife Financial Services,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Manulife Financial Services
 * and its suppliers and may be covered by patents in process, 
 * and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Manulife Financial Services.
 */
package com.manulife.hk;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;

import org.junit.Assert;
import org.junit.FixMethodOrder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.MethodSorters;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.SpringApplication;

/**
 * Sample application test file
 * <AUTHOR> Sharma
 */
@FixMethodOrder(MethodSorters.NAME_ASCENDING)
@RunWith(MockitoJUnitRunner.class)
public class ApplicationTest {

    @Test
    public void appCreated() {
        Application application = new Application();
        Assert.assertNotNull(application);
    }

    @Test
    public void appStarted() throws Exception {

        try (MockedStatic<SpringApplication> mock = Mockito.mockStatic(SpringApplication.class)) {
            Application.main(new String[]{});

            mock.verify(
                () -> SpringApplication.run(eq(Application.class), any(String[].class)),
                times(1)
            );
        }

        // After the static method is invoked, verify that it was called by calling it again after verifyStatic() call
        Application.main(new String[]{});
    }

}