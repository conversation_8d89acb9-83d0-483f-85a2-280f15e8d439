spring:
# 12FA: Caching for long running services, defaults to none
  cache:
    type: none
#    cache-names:
# Uncomment if you are not using RabbitMQ
#  autoconfigure:
#    exclude: org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration
# Security : replace name/password
  security:
    user:
      name: test
      password: test123 
# 12FA: Eureka - Registry   : Default
# 12FA: Logging        
logging:
  level:
    org.springframework: info
    com.manulife.ap: info
# 12FAE: Management endpoints    
management:
  metrics:
    enabled: true
  export:
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: heapdump,loggers,threaddump,info,metrics,health,prometheus
  endpoint:
    metrics:
      enabled: true
    prometheus:
      enabled: true
  info:
    git:
      mode: full
