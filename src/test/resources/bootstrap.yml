spring:
  cloud:
   kubernetes:
    config:
     enabled: false
  application:
    name: hk-ifp-uw-core-service
  #Only for local build uncomment below lines.
#  cloud:
#    config:
#      uri: http://localhost:8888


---
# Uncomment the following to suppress Azure Key Vault and Azure Service Bus in your integration testing
azure:
  keyvault:
    enabled: false # disable azure key vault
spring:
  cloud:
    azure:
      servicebus:
        enabled: false # disable azure service bus

---
# Uncomment the following to suppress RSF Config Client only
rsf:
  config:
    client:
      enabled: false # disable rsf config client