    ___      _         _____         _                 _                     _____  __  __ _          
   / _ \    (_)       |_   _|       | |               | |                   |  _  |/ _|/ _(_)         
  / /_\ \___ _  __ _    | | ___  ___| |__  _ __   ___ | | ___   __ _ _   _  | | | | |_| |_ _  ___ ___ 
  |  _  / __| |/ _` |   | |/ _ \/ __| '_ \| '_ \ / _ \| |/ _ \ / _` | | | | | | | |  _|  _| |/ __/ _ \
  | | | \__ \ | (_| |   | |  __/ (__| | | | | | | (_) | | (_) | (_| | |_| | \ \_/ / | | | | | (_|  __/
  \_| |_/___/_|\__,_|   \_/\___|\___|_| |_|_| |_|\___/|_|\___/ \__, |\__, |  \___/|_| |_| |_|\___\___|
                                                                __/ | __/ |                           
                                                               |___/ |___/     
                                                               			${spring-boot.formatted-version}                       