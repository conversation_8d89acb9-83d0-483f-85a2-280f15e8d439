common:
  token_url: https://login.microsoftonline.com/5d3e2773-e07f-4432-a630-1a0f68a28a05/oauth2/v2.0/token
  client_id: d4aacef8-bf73-49c0-a647-905a710fe3b7
  secret: ****************************************

---
spring:
  application:
    name: hk-ifp-uw-core-service
  jpa:
    show-sql: true
  cache:
    type: none
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    type: com.zaxxer.hikari.HikariDataSource
    url: ***********************************************************************************************************
    username: hkit
    password: 'ViNvX%\p3ThTf=/nFbnd'
    hikari:
      pool-name: nb_uw_core_conn_pool
      minimum-idle: 10
      maximum-pool-size: 80
      auto-commit: true
      max-lifetime: 1800000
  flyway:
    baseline-on-migrate: true
    baseline-version: 0
    clean-disabled: false
    out-of-order: true
    enabled: true  # 重新启用Flyway，已添加SQL Server支持
    locations:
      - classpath:cicd/flyway/db
    schemas:
      - dbo
  sleuth:
    log:
      slf4j:
        whitelisted-mdc-keys:
          - policy-no-key
          - request-id
          - request-date
          - user-id
    propagation-keys:
      - policy-no-key
      - request-id
      - request-date
      - user-id
  lifecycle:
    timeout-per-shutdown-phase: 6s #graceful shutdown period

logging:
  level:
    root: debug
    org.springframework: debug
    com.azure.core.amqp.implementation: off
    com.azure.messaging.servicebus: info
  config: src/main/resources/logback-local.xml
management:
  endpoints:
    web:
      exposure:
        include: heapdump,loggers,threaddump,info,metrics,health,refresh
  endpoint:
    health:
      show-details: always
  info:
    git:
      mode: full

rsf.core.gracefulShutdown.enabled: true


#------------------ Different Config ---------------------#

azure:
  activedirectory:
    tenant-id: 5d3e2773-e07f-4432-a630-1a0f68a28a05
    client-id: fb9cf199-b3b2-40c6-beb5-0342f433eaa3

server:
  shutdown: graceful

nb:
  extLog:
    singleStorage: 50
