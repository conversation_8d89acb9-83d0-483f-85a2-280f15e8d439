
spring:
  security:
    user:
      name: test
      password: test123

# 12FAE: Management endpoints    
management:
  metrics:
    enabled: true
  export:
    prometheus:
      enabled: true
  endpoints:
    web:
      exposure:
        include: heapdump,loggers,threaddump,info,metrics,health,prometheus
  endpoint:
    health:
      show-details: always
    metrics:
      enabled: true
    prometheus:
      enabled: true
  info:
    git:
      mode: full

# Custom configuration for configuring list of CORS allowed origins
# for example, add the Angular website domain to allow cross-origin-request via ajax
cors:
  allowed:
    origins: "*"