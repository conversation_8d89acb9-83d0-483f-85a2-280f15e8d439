package com.manulife.hk.domain;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "Base Model for all cases")
public class CaseBaseModel {
    private String caseNo;
    private String caseType;
    private String policyNo;
    private LocalDateTime createDate;
    private String createBy;
    private LocalDateTime lastUpdDate;
    private String lastUpdBy;

}