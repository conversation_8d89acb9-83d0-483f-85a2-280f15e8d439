package com.manulife.hk.domain.pending;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "")
public class PendingDocument extends PendingMemo {
    private String docCode;
    private String docDesc;
    private String turnDay;
    private String docStatus;
    private LocalDate docFollowUpDate;

}