package com.manulife.hk.domain.pending;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "")
public class Note extends PendingMemo {
    private String seqNo;
    private String noteCode;
    private String noteDesc;

}