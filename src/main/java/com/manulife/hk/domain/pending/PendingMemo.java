package com.manulife.hk.domain.pending;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "")
public class PendingMemo {
    private String seqNo;
    private String pendingItemType;
    private LocalDateTime createDate;
    private String lastUpdateBy;
    private LocalDateTime lastUpdateDate;
    private String requestSeq;
    private LocalDate requestDate;
    private LocalDate receiveDate;
    private String remark;
    private String status;


}