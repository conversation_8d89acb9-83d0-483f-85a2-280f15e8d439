package com.manulife.hk.domain.partner;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "")
public class Address extends Place{
    private String addLine1;
    private String addLine2;
    private String addLine3;
    private String addLine4;
    private String district;
    private String postalCode;
}