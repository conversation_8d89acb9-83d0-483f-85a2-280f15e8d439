package com.manulife.hk.domain.partner;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "")
public class MedicalCharge {
    private String chargeCode;
    private String description;
    private BigDecimal medicalChargeAmt;
    private String medicalChargeType;
    private String status;

}