package com.manulife.hk.domain.partner;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "")
public class Doctor extends UnderwritingPartner{
    private String payee;
    private String bankAccount;
    private String payAmount;
    private List<MedicalCharge> medicalChargeList;

}