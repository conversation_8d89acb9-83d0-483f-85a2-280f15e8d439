package com.manulife.hk.domain.partner;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "Doctor Bill")
public class DoctorBill {
    @ApiModelProperty(value="",name="",example="null")
    private String status;
    private String payInd;@ApiModelProperty(value="testDate",name="Test date of the exam item in the bill",example="null")
    private LocalDate testDate;
    @ApiModelProperty(value="receiveDate",name="",example="null")
    private LocalDate receiveDate;
    @ApiModelProperty(value="issueDate",name="Received date of the bill",example="null")
    private LocalDate issueDate;
    private Doctor doctor;
    private String hklDNO;
    @ApiModelProperty(value="surName",name="",example="null")
    private String surName;
    @ApiModelProperty(value="givenName",name="",example="null")
    private String givenName;
}