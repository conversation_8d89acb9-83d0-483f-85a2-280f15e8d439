package com.manulife.hk.domain.partner;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "")
public class UnderwritingPartner {
    private String id;
    private String name;
    private String remark;
    private String status;
    private Address addAddressInfo;
    
    private Phone telNoListPhone;
    private Phone fax;

}