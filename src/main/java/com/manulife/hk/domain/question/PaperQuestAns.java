package com.manulife.hk.domain.question;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "")
public class PaperQuestAns extends UWQuestion{
    private String policyNo;
    private String questionId;
    private String answer;

}