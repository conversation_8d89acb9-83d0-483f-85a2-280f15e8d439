package com.manulife.hk.domain.premium;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "extra premium coverage")
public class ExtraPremiumCoverage {

    @ApiModelProperty(value="",name="planCode",example="null")
    private String planCode;
    private String trancheId;
    private String cvgClass;
    private List<HospSubStandard> hospitalSubStdList;
    private VHISLob vhisLob;
    private String occClass;
    @ApiModelProperty(value="Face Amount of the plan",name="faceAmount",example="null")
    private BigDecimal faceAmount;
    private BigDecimal premPercentage;
    private BigDecimal premFlat;
    private BigDecimal tempFlat;
    private Integer year;
    private String loadingReason;
    private String decision;
    
    private List<FacultativeReason> facultativeReasonList;

}