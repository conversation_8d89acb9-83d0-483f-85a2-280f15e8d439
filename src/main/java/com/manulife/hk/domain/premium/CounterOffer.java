package com.manulife.hk.domain.premium;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "")
public class CounterOffer {
    private String impairmentCode;
    private String campaignCode;
    private List<ExtraPremiumCoverage> extraPremiumCoverage;

}