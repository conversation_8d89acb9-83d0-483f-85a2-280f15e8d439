package com.manulife.hk.domain.letter;


import com.manulife.hk.domain.partner.UnderwritingPartner;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "ap letter")
public class APLetter {

    @ApiModelProperty(value="seqNo",name="seqNo",example="null")
    private String seqNo;
    @ApiModelProperty(value="Name of the APS letter",name="letterName",example="null")
    private String letterName;
    @ApiModelProperty(value="Status of the letter",name="status",example="null")
    private String status;
    @ApiModelProperty(value="letterTemplate",name="remarks",example="null")
    private String remarks;
    @ApiModelProperty(value="UnderwritingPartner",name="UnderwritingPartner",example="null")
    private UnderwritingPartner letterRecipient;
    @ApiModelProperty(value="Letter template available to select",name="letterTemplate",example="null")
    private LetterTemplate letterTemplate;

}