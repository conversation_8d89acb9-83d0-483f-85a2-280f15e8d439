package com.manulife.hk.domain.underwriting;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "")
public class ReferralCase {
    private String status;
    private String referredTo;
    private String referredBy;
    private String type;
    private LocalDate replyDate;
    private String itemAttribute;
    private String remark;

}