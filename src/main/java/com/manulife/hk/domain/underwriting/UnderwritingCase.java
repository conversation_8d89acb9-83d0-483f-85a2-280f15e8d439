package com.manulife.hk.domain.underwriting;

import com.manulife.hk.domain.CaseBaseModel;
import com.manulife.hk.domain.letter.APLetter;
import com.manulife.hk.domain.pending.PendingMemo;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(description = "")
public class UnderwritingCase extends CaseBaseModel {
    private String uwDecision;
    private String uwDecisionBy;
    private LocalDateTime uwDecisionDate;
    private List<CaseComment> caseCommentList;
    private List<PendingMemo> pendingMemoList;
    private List<APLetter> apsLetters;
    private List<ReferralCase> referralCaseList;

}