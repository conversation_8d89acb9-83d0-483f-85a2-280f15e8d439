/*
 * SwaggerDocumentationConfig
 *
 * Copyright (c) 2020 Manulife International Ltd.
 *
 * Description:
 *
 *
 * Maintenance History
 *
 * YYMMDD Who              Reason
 * ====== ================ ==================================================================================================================================
 * 200113 Jeffrey Cheung   Initial Development
 */

package com.manulife.hk.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;

@Configuration
public class SwaggerDocumentationConfig implements WebMvcConfigurer {


	ApiInfo apiInfo() {
		return new ApiInfoBuilder().title("UW CORE SERVICE").description("HK UW CORE SERVICE REST Endpoints")
				.version("1.9.0")
				.contact(new Contact("", "","<EMAIL>"))
				.build();
	}


}