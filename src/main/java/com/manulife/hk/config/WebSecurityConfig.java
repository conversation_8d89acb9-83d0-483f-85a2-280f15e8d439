/*IFP_24_RL01_NBUW_11658 comment start
 * WebSecurityConfig
 *
 * Copyright (c) 2023 Manulife International Ltd.
 *
 * Description: Update Response Header with ReferrerPolicy.
 *
 *
 * Maintenance History
 *
 * YYMMDD Who              Reason
 * ====== ================ ==================================================================================================================================
 * 230711 (MITDC CD)Bruce Hu IFP_23_RL04_NBUW_10609 - Pen test[14-Jul completed]
 * 231110 (MITDC CD)Leo Li   IFP_24_RL01_NBUW_11658 - RSF UPGRADE to 2.0
 * 250225 (MITDC CD) David Yang     IFP_25_RL02_NBUW_39954 - Save customer page finetune - Remove oauth2 token for improving performance
 */

/*
import com.azure.spring.aad.webapi.AADResourceServerWebSecurityConfigurerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;


@Configuration
@Slf4j
public class WebSecurityConfig extends AADResourceServerWebSecurityConfigurerAdapter {

    @Override
    protected void configure(HttpSecurity http) throws Exception {
        super.configure(http);
        http.headers()
                .referrerPolicy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN);
    }

}
 IFP_24_RL01_NBUW_11658 comment end*/
//IFP_24_RL01_NBUW_11658 add start
package com.manulife.hk.config;

import com.azure.spring.cloud.autoconfigure.implementation.aad.security.AadResourceServerHttpSecurityConfigurer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.header.writers.ReferrerPolicyHeaderWriter;

@Configuration
@Slf4j
public class WebSecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.apply(AadResourceServerHttpSecurityConfigurer.aadResourceServer());
        http.csrf(csrfConfigurer -> csrfConfigurer.ignoringRequestMatchers("/**"))
                .authorizeHttpRequests(authz ->
                        authz.requestMatchers("/apr/*").authenticated()
                                .requestMatchers("/uw/query/*").authenticated()
                                .anyRequest().permitAll()
                )
                .httpBasic(Customizer.withDefaults())
                .headers(httpSecurityHeadersConfigurer -> {
                    httpSecurityHeadersConfigurer.referrerPolicy(
                            referrerPolicyConfig -> {
                                referrerPolicyConfig
                                        .policy(ReferrerPolicyHeaderWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN);
                            });
                });
        return http.build();
    }
}