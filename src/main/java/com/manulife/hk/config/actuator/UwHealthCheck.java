/*
 * NbHealthCheck
 *
 * Copyright (c) 2020 Manulife International Ltd.
 *
 * Description: Service health monitoring.
 *
 *
 * Maintenance History
 *
 * YYMMDD Who                       Reason
 * ====== ========================  ==================================================================================================================================
 * 200113 (Cognizant)Somnath Dutta  <mode-code> - API to determine STP result (https://jira.ap.manulife.com/browse/SB-14216) 
 */

package com.manulife.hk.config.actuator;

import org.springframework.boot.actuate.health.AbstractHealthIndicator;
import org.springframework.boot.actuate.health.Health;
import org.springframework.stereotype.Component;

@Component("uwHealthCheck")
public class UwHealthCheck extends AbstractHealthIndicator {

	@Override
	protected void doHealthCheck(Health.Builder builder) throws Exception {

		builder.up().withDetail("service-status", "Service can receive requests !!");

	}

}
