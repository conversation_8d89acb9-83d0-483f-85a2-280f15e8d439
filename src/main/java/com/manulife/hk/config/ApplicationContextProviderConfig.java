/*
 * ApplicationContextProviderConfig
 * 
 * Copyright (c) 2019 Manulife International Ltd.
 *
 * Description:
 * 
 *
 * Maintenance History
 *
 * YYMMDD Who              Reason
 * ====== ================ ==================================================================================================================================
 * 200113 Vimal V   	   Initial Development
 */

package com.manulife.hk.config;

import com.manulife.hk.util.BeanUtil;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;


@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class ApplicationContextProviderConfig implements ApplicationContextAware {

	public void setApplicationContext(ApplicationContext ctx) {
		BeanUtil.setApplicationContext(ctx);
	}
}