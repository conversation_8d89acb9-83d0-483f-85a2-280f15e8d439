/*
 * BeanUtil
 *
 * Copyright (c) 2020 Manulife International Ltd.
 *
 * Description:
 *
 *
 * Maintenance History
 *
 * YYMMDD Who              	Reason
 * ====== ================ 	==================================================================================================================================
 * 200421 Vimal				IFP_20_RL04_SB_NB_10956 - Initial Development
 * 230202 (MITDC CD) Ivan <PERSON>          IFP_23_RL02_NBUW_2347 - Successive Policyowner, Contingent Life Insured
 * 240906 (MITDC CD) Kelly Xiao         IFP_24_RL05_NBUW_22006 - [Genesis & WIOP3] - Legacy Planning
 */
package com.manulife.hk.util;


import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ModifierUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import org.springframework.context.ApplicationContext;

import java.lang.reflect.Field;

public class BeanUtil {

	private BeanUtil() {
		throw new UnsupportedOperationException();
	}
	private static ApplicationContext context;

	public static void setApplicationContext(ApplicationContext applicationContext) {
		context = applicationContext;
	}

	public static <T extends Object> T getBean(Class<T> beanClass) {
		return context.getBean(beanClass);
	}

	/**
	 * Determine whether the Bean is a non-null object. A non-null object means that it is not {null} or an object that contains a non-{null} attribute
	 *
	 * @param bean             Bean object
	 * @param ignoreFiledNames Ignore checked field names
	 * @return is not empty，{true} - non-null  / {false} - null
	 */
	public static boolean isNotEmpty(Object bean, String... ignoreFiledNames) {
		return false == isEmpty(bean, ignoreFiledNames);
	}

	/**
	 * Determine whether the Bean is an empty object. An empty object means that it is {null} or that all properties are {null}
	 * This method does not judge static attributes(Contain the log field of @Slf4j)
	 *
	 * @param bean             Bean object
	 * @param ignoreFiledNames Ignore checked field names
	 * @return is empty，{true} - non-null  / {false} - null
	 */
	public static boolean isEmpty(Object bean, String... ignoreFiledNames) {
		if (null != bean) {
			for (Field field : ReflectUtil.getFields(bean.getClass())) {
				if (ModifierUtil.isStatic(field)) {
					continue;
				}
				if ((false == ArrayUtil.contains(ignoreFiledNames, field.getName()))
						&& ObjectUtil.isNotEmpty(ReflectUtil.getFieldValue(bean, field))) {
					return false;
				}
			}
		}
		return true;
	}
	/**
	 *Determine whether the attributes passed in are not empty
	 *
	 *
	 * @param bean             Bean object
	 * @param filedNames Ignore checked field names
	 * @return have empty or null，{flase} - all not empty and null {true}
	 */
	public static boolean isAllinFiledsNotEmpty(Object bean, String... filedNames) {
		if (null != bean) {
			for (Field field : ReflectUtil.getFields(bean.getClass())) {
				if (ModifierUtil.isStatic(field)) {
					continue;
				}
				if (ArrayUtil.contains(filedNames, field.getName())
						&& ObjectUtil.isEmpty(ReflectUtil.getFieldValue(bean, field))) {
					return false;
				}
			}
		}
		return true;
	}

	/**
	 *Determine whether the attributes passed in all empty
	 *
	 *
	 * @param bean             Bean object
	 * @param filedNames Ignore checked field names
	 * @return all empty or null，{true} -not all empty and null {false}
	 */
	public static boolean isAllinFiledsEmpty(Object bean, String... filedNames) {
		if (null != bean) {
			for (Field field : ReflectUtil.getFields(bean.getClass())) {
				if (ModifierUtil.isStatic(field)) {
					continue;
				}
				if (ArrayUtil.contains(filedNames, field.getName())
						&& ObjectUtil.isNotEmpty(ReflectUtil.getFieldValue(bean, field))) {
					return false;
				}
			}
		}
		return true;
	}
	/**
	 * Determine whether the Bean contains a property whose value is {null}
	 * The object itself is {null} also returns true
	 *
	 * @param bean             Bean object
	 * @param ignoreFiledNames Ignore checked field names
	 * @return Whether to include attributes with value {null}, {true} - include / {false} - not include
	 */
	public static boolean hasNullField(Object bean, String... ignoreFiledNames) {
		if (null == bean) {
			return true;
		}
		for (Field field : ReflectUtil.getFields(bean.getClass())) {
			if (ModifierUtil.isStatic(field)) {
				continue;
			}
			if ((false == ArrayUtil.contains(ignoreFiledNames, field.getName()))
					&& ObjectUtil.isEmpty(ReflectUtil.getFieldValue(bean, field))) {
				return true;
			}
		}
		return false;
	}

}
