<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-01-01T00:00:00.000Z" agent="5.0" etag="xxx" version="24.0.0" type="device">
  <diagram name="Database ERD" id="database-erd">
    <mxGraphModel dx="2074" dy="1114" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="3300" pageHeight="4681" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- Case Base Model and Inheritance -->
        <mxCell id="CaseBaseModel" value="CaseBaseModel&#xa;&#xa;caseNo (PK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="100" y="100" width="200" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="UnderwritingCase" value="UnderwritingCase&#xa;&#xa;caseNo (PK,FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#fff2cc;strokeColor=#d6b656;" vertex="1" parent="1">
          <mxGeometry x="400" y="100" width="200" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="PUIinquiryCase" value="PUIinquiryCase&#xa;&#xa;caseNo (PK,FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="100" y="300" width="180" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="NBInquiryCase" value="NBInquiryCase&#xa;&#xa;caseNo (PK,FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="300" y="300" width="180" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="NewBusinessCase" value="NewBusinessCase&#xa;&#xa;caseNo (PK,FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="500" y="300" width="180" height="140" as="geometry" />
        </mxCell>
        
        <!-- UnderwritingPartner and its inheritance -->
        <mxCell id="UnderwritingPartner" value="UnderwritingPartner&#xa;&#xa;id (PK): nvarchar(20)&#xa;biz_phone_id (FK): nvarchar(20)&#xa;fax_phone_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="800" y="100" width="220" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="Doctor" value="Doctor&#xa;&#xa;id (PK): nvarchar(20)&#xa;partner_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="700" y="350" width="180" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="Hospital" value="Hospital&#xa;&#xa;id (PK): nvarchar(20)&#xa;partner_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="900" y="350" width="180" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="InsuranceCompany" value="InsuranceCompany&#xa;&#xa;id (PK): nvarchar(20)&#xa;partner_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1100" y="350" width="180" height="140" as="geometry" />
        </mxCell>
        
        <!-- Phone -->
        <mxCell id="Phone" value="Phone&#xa;&#xa;id (PK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#fad7ac;strokeColor=#b46504;" vertex="1" parent="1">
          <mxGeometry x="1200" y="100" width="180" height="120" as="geometry" />
        </mxCell>
        
        <!-- Case Components -->
        <mxCell id="CaseComment" value="CaseComment&#xa;&#xa;id (PK): nvarchar(20)&#xa;case_no (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="100" y="550" width="180" height="140" as="geometry" />
        </mxCell>
        
        <mxCell id="PendingMemo" value="PendingMemo&#xa;&#xa;id (PK): nvarchar(20)&#xa;case_no (FK): nvarchar(20)&#xa;doctor_bill_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="300" y="550" width="200" height="160" as="geometry" />
        </mxCell>
        
        <mxCell id="APLetter" value="APLetter&#xa;&#xa;id (PK): nvarchar(20)&#xa;case_no (FK): nvarchar(20)&#xa;letter_recipient_id (FK): nvarchar(20)&#xa;letter_template_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="520" y="550" width="220" height="180" as="geometry" />
        </mxCell>
        
        <mxCell id="ReferralCase" value="ReferralCase&#xa;&#xa;id (PK): nvarchar(20)&#xa;case_no (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="760" y="550" width="180" height="140" as="geometry" />
        </mxCell>
        
        <!-- Medical Related -->
        <mxCell id="MedicalCharge" value="MedicalCharge&#xa;&#xa;id (PK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f0f0f0;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1400" y="350" width="180" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="DoctorBill" value="DoctorBill&#xa;&#xa;id (PK): nvarchar(20)&#xa;doctor_id (FK): nvarchar(20)&#xa;insurance_company_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f0f0f0;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1400" y="550" width="220" height="160" as="geometry" />
        </mxCell>
        
        <!-- User and Customer -->
        <mxCell id="UserSimple" value="UserSimple&#xa;&#xa;id (PK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e6f3ff;strokeColor=#0066cc;" vertex="1" parent="1">
          <mxGeometry x="100" y="800" width="180" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="Customer" value="Customer&#xa;&#xa;id (PK): nvarchar(20)&#xa;user_id (FK): nvarchar(20)&#xa;case_no (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#e6f3ff;strokeColor=#0066cc;" vertex="1" parent="1">
          <mxGeometry x="350" y="800" width="180" height="160" as="geometry" />
        </mxCell>
        
        <!-- Place and Address -->
        <mxCell id="Place" value="Place&#xa;&#xa;id (PK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ccffcc;strokeColor=#009900;" vertex="1" parent="1">
          <mxGeometry x="600" y="800" width="180" height="120" as="geometry" />
        </mxCell>
        
        <mxCell id="Address" value="Address&#xa;&#xa;id (PK): nvarchar(20)&#xa;place_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ccffcc;strokeColor=#009900;" vertex="1" parent="1">
          <mxGeometry x="850" y="800" width="180" height="140" as="geometry" />
        </mxCell>
        
        <!-- Letter Template -->
        <mxCell id="LetterTemplate" value="LetterTemplate&#xa;&#xa;id (PK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffccff;strokeColor=#cc00cc;" vertex="1" parent="1">
          <mxGeometry x="1100" y="800" width="180" height="120" as="geometry" />
        </mxCell>
        
        <!-- Inheritance Relationships -->
        <mxCell id="inherit1" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="PUIinquiryCase" target="CaseBaseModel">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="inherit2" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="NBInquiryCase" target="CaseBaseModel">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="inherit3" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="NewBusinessCase" target="CaseBaseModel">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="inherit4" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="UnderwritingCase" target="CaseBaseModel">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="inherit5" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="Doctor" target="UnderwritingPartner">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="inherit6" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="Hospital" target="UnderwritingPartner">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="inherit7" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="InsuranceCompany" target="UnderwritingPartner">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="inherit8" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="Address" target="Place">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>
        
        <!-- Composition Relationships -->
        <mxCell id="comp1" value="" style="endArrow=diamondThin;endFill=1;endSize=24;html=1;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="CaseComment" target="UnderwritingCase">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="190" y="500" as="sourcePoint" />
            <mxPoint x="500" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="comp2" value="" style="endArrow=diamondThin;endFill=1;endSize=24;html=1;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="PendingMemo" target="UnderwritingCase">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="400" y="500" as="sourcePoint" />
            <mxPoint x="500" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="comp3" value="" style="endArrow=diamondThin;endFill=1;endSize=24;html=1;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="APLetter" target="UnderwritingCase">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="630" y="500" as="sourcePoint" />
            <mxPoint x="500" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <mxCell id="comp4" value="" style="endArrow=diamondThin;endFill=1;endSize=24;html=1;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="ReferralCase" target="UnderwritingCase">
          <mxGeometry width="160" relative="1" as="geometry">
            <mxPoint x="850" y="500" as="sourcePoint" />
            <mxPoint x="500" y="300" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
        <!-- Association Relationships -->
        <mxCell id="assoc1" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="UnderwritingPartner" target="Phone">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc2" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=0;exitY=0.5;entryX=1;entryY=0.5;" edge="1" parent="1" source="MedicalCharge" target="Doctor">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc3" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="DoctorBill" target="Doctor">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc4" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=0;exitY=0.5;entryX=1;entryY=0.5;" edge="1" parent="1" source="DoctorBill" target="InsuranceCompany">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc5" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="UserSimple" target="Customer">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc6" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="Customer" target="UnderwritingCase">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        
        <mxCell id="assoc7" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="APLetter" target="LetterTemplate">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <!-- Additional Entities for Premium and Counter Offer -->
        <mxCell id="CounterOffer" value="CounterOffer&#xa;&#xa;id (PK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffcccc;strokeColor=#ff0000;" vertex="1" parent="1">
          <mxGeometry x="1400" y="800" width="180" height="120" as="geometry" />
        </mxCell>

        <mxCell id="ExtraPremiumCoverage" value="ExtraPremiumCoverage&#xa;&#xa;id (PK): nvarchar(20)&#xa;vhis_lob_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffcccc;strokeColor=#ff0000;" vertex="1" parent="1">
          <mxGeometry x="1650" y="800" width="200" height="140" as="geometry" />
        </mxCell>

        <mxCell id="VHISLob" value="VHISLob&#xa;&#xa;id (PK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ccccff;strokeColor=#0000ff;" vertex="1" parent="1">
          <mxGeometry x="1900" y="800" width="180" height="120" as="geometry" />
        </mxCell>

        <mxCell id="HospSubStandard" value="HospSubStandard&#xa;&#xa;id (PK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ccccff;strokeColor=#0000ff;" vertex="1" parent="1">
          <mxGeometry x="1650" y="1000" width="180" height="120" as="geometry" />
        </mxCell>

        <mxCell id="FacultativeReason" value="FacultativeReason&#xa;&#xa;id (PK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ccccff;strokeColor=#0000ff;" vertex="1" parent="1">
          <mxGeometry x="1900" y="1000" width="180" height="120" as="geometry" />
        </mxCell>

        <!-- Pending Memo Inheritance -->
        <mxCell id="PendingDocument" value="PendingDocument&#xa;&#xa;id (PK): nvarchar(20)&#xa;pending_memo_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffffcc;strokeColor=#ffcc00;" vertex="1" parent="1">
          <mxGeometry x="200" y="750" width="200" height="140" as="geometry" />
        </mxCell>

        <mxCell id="F856" value="F856&#xa;&#xa;id (PK): nvarchar(20)&#xa;pending_memo_id (FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#ffffcc;strokeColor=#ffcc00;" vertex="1" parent="1">
          <mxGeometry x="420" y="750" width="200" height="140" as="geometry" />
        </mxCell>

        <!-- More Case Types -->
        <mxCell id="PolicyChangeCase" value="PolicyChangeCase&#xa;&#xa;caseNo (PK,FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="700" y="300" width="180" height="140" as="geometry" />
        </mxCell>

        <mxCell id="ReconsiderationCase" value="ReconsiderationCase&#xa;&#xa;caseNo (PK,FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="900" y="300" width="180" height="140" as="geometry" />
        </mxCell>

        <mxCell id="NondisclosureCase" value="NondisclosureCase&#xa;&#xa;caseNo (PK,FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1100" y="300" width="180" height="140" as="geometry" />
        </mxCell>

        <mxCell id="TBC" value="TBC&#xa;&#xa;caseNo (PK,FK): nvarchar(20)&#xa;JSON_CONTEXT: nvarchar(max)&#xa;create_date: datetime&#xa;last_update_date: datetime&#xa;create_by: varchar(50)&#xa;update_by: varchar(50)" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1300" y="300" width="180" height="140" as="geometry" />
        </mxCell>

        <!-- Additional Inheritance Relationships -->
        <mxCell id="inherit9" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="PolicyChangeCase" target="CaseBaseModel">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>

        <mxCell id="inherit10" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="ReconsiderationCase" target="CaseBaseModel">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>

        <mxCell id="inherit11" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="NondisclosureCase" target="CaseBaseModel">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>

        <mxCell id="inherit12" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="TBC" target="CaseBaseModel">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>

        <mxCell id="inherit13" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="PendingDocument" target="PendingMemo">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>

        <mxCell id="inherit14" value="" style="endArrow=block;endFill=0;html=1;edgeStyle=orthogonalEdgeStyle;align=left;verticalAlign=bottom;exitX=0.5;exitY=0;entryX=0.5;entryY=1;" edge="1" parent="1" source="F856" target="PendingMemo">
          <mxGeometry x="-1" relative="1" as="geometry" />
        </mxCell>

        <!-- Additional Association Relationships -->
        <mxCell id="assoc8" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="CounterOffer" target="ExtraPremiumCoverage">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc9" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=1;exitY=0.5;entryX=0;entryY=0.5;" edge="1" parent="1" source="ExtraPremiumCoverage" target="VHISLob">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc10" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="ExtraPremiumCoverage" target="HospSubStandard">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

        <mxCell id="assoc11" value="" style="endArrow=none;html=1;edgeStyle=orthogonalEdgeStyle;exitX=0.5;exitY=1;entryX=0.5;entryY=0;" edge="1" parent="1" source="VHISLob" target="FacultativeReason">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
