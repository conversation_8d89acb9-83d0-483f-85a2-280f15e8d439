#!/bin/bash
clear

# bootstrap Azure Key Vault configuration as config server depends on it to perform cryptographic operation
# managed by Ops and injected through release pipeline
java \
-Dazure.keyvault.enabled=$AKV_ENABLE \
-Dazure.keyvault.kek-name=$AKV_KEK_NAME \
-Dazure.keyvault.dek-name=$AKV_DEK_NAME \
-Dazure.keyvault.uri=$AKV_URI \
-Dazure.keyvault.client-id=$AKV_CLIENT_ID \
-Dazure.keyvault.client-key=$AKV_CLIENT_KEY \
-Dazure.keyvault.tenant-id=$AKV_TENANT_ID \
-Dspring.profiles.active=aks,dev \
-jar target/demo-order-app-0.0.3-SNAPSHOT.jar