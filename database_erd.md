# 数据库实体关系图 (ERD)

## 核心案件继承体系

```mermaid
erDiagram
    CaseBaseModel ||--|| PUIinquiryCase : inherits
    CaseBaseModel ||--|| NBInquiryCase : inherits
    CaseBaseModel ||--|| NewBusinessCase : inherits
    CaseBaseModel ||--|| PolicyChangeCase : inherits
    CaseBaseModel ||--|| ReconsiderationCase : inherits
    CaseBaseModel ||--|| NondisclosureCase : inherits
    CaseBaseModel ||--|| TBC : inherits
    CaseBaseModel ||--|| UnderwritingCase : inherits

    CaseBaseModel {
        nvarchar caseNo PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    UnderwritingCase {
        nvarchar caseNo PK,FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }
```

## 核保案件组合关系

```mermaid
erDiagram
    UnderwritingCase ||--o{ CaseComment : contains
    UnderwritingCase ||--o{ PendingMemo : contains
    UnderwritingCase ||--o{ APLetter : contains
    UnderwritingCase ||--o{ ReferralCase : contains

    UnderwritingCase {
        nvarchar caseNo PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    CaseComment {
        nvarchar id PK
        nvarchar case_no FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    PendingMemo {
        nvarchar id PK
        nvarchar case_no FK
        nvarchar doctor_bill_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    APLetter {
        nvarchar id PK
        nvarchar case_no FK
        nvarchar letter_recipient_id FK
        nvarchar letter_template_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    ReferralCase {
        nvarchar id PK
        nvarchar case_no FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }
```

## 合作伙伴继承体系

```mermaid
erDiagram
    UnderwritingPartner ||--|| Hospital : inherits
    UnderwritingPartner ||--|| InsuranceCompany : inherits
    UnderwritingPartner ||--|| Doctor : inherits
    
    UnderwritingPartner ||--o| Phone : "biz_phone"
    UnderwritingPartner ||--o| Phone : "fax_phone"

    UnderwritingPartner {
        nvarchar id PK
        nvarchar biz_phone_id FK
        nvarchar fax_phone_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    Phone {
        nvarchar id PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    Hospital {
        nvarchar id PK
        nvarchar partner_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    Doctor {
        nvarchar id PK
        nvarchar partner_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    InsuranceCompany {
        nvarchar id PK
        nvarchar partner_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }
```

## 地址和地点关系

```mermaid
erDiagram
    Place ||--|| Address : inherits
    Customer ||--o{ CustomerAddress : aggregates
    CustomerAddress }o--|| Address : references

    Place {
        nvarchar id PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    Address {
        nvarchar id PK
        nvarchar place_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    Customer {
        nvarchar id PK
        nvarchar user_id FK
        nvarchar case_no FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    CustomerAddress {
        nvarchar customer_id PK,FK
        nvarchar address_id PK,FK
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }
```

## 医疗相关实体关系

```mermaid
erDiagram
    Doctor ||--o{ DoctorMedicalCharge : aggregates
    DoctorMedicalCharge }o--|| MedicalCharge : references
    Doctor ||--o{ DoctorBill : creates
    DoctorBill }o--|| InsuranceCompany : "billed_to"
    PendingMemo }o--o| DoctorBill : references

    Doctor {
        nvarchar id PK
        nvarchar partner_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    MedicalCharge {
        nvarchar id PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    DoctorMedicalCharge {
        nvarchar doctor_id PK,FK
        nvarchar medical_charge_id PK,FK
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    DoctorBill {
        nvarchar id PK
        nvarchar doctor_id FK
        nvarchar insurance_company_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }
```

## 待处理事项继承体系

```mermaid
erDiagram
    PendingMemo ||--|| PendingDocument : inherits
    PendingMemo ||--|| F856 : inherits

    PendingMemo {
        nvarchar id PK
        nvarchar case_no FK
        nvarchar doctor_bill_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    PendingDocument {
        nvarchar id PK
        nvarchar pending_memo_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    F856 {
        nvarchar id PK
        nvarchar pending_memo_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }
```

## 信函和模板关系

```mermaid
erDiagram
    LetterTemplate ||--o{ APLetter : "based_on"
    UnderwritingPartner ||--o{ APLetter : "recipient"

    LetterTemplate {
        nvarchar id PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    APLetter {
        nvarchar id PK
        nvarchar case_no FK
        nvarchar letter_recipient_id FK
        nvarchar letter_template_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }
```

## 反建议和保费覆盖关系

```mermaid
erDiagram
    CounterOffer ||--o{ CounterOfferExtraPremiumCoverage : aggregates
    CounterOfferExtraPremiumCoverage }o--|| ExtraPremiumCoverage : references
    ExtraPremiumCoverage }o--o| VHISLob : associates
    ExtraPremiumCoverage ||--o{ ExtraPremiumCoverageHospSubStd : aggregates
    ExtraPremiumCoverageHospSubStd }o--|| HospSubStandard : references
    ExtraPremiumCoverage ||--o{ ExtraPremiumCoverageFacReason : aggregates
    ExtraPremiumCoverageFacReason }o--|| FacultativeReason : references

    CounterOffer {
        nvarchar id PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    ExtraPremiumCoverage {
        nvarchar id PK
        nvarchar vhis_lob_id FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    VHISLob {
        nvarchar id PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    HospSubStandard {
        nvarchar id PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    FacultativeReason {
        nvarchar id PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }
```

## 用户和客户关系

```mermaid
erDiagram
    UserSimple ||--o{ Customer : associates
    Customer }o--o| UnderwritingCase : associates

    UserSimple {
        nvarchar id PK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }

    Customer {
        nvarchar id PK
        nvarchar user_id FK
        nvarchar case_no FK
        nvarchar JSON_CONTEXT
        datetime create_date
        datetime last_update_date
        varchar create_by
        varchar update_by
    }
```
