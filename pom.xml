<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.manulife.hk</groupId>
	<artifactId>hk-ifp-uw-core-service</artifactId>
	<version>1.0.1</version>
	<packaging>jar</packaging>
	<name>hk-ifp-uw-core-service</name>
	<url>https://mfc.sharepoint.com/sites/asiatemp/asiadigital/DE/Microservices/Home.aspx</url>
	<description>Customize your project based on generated template file</description>

	<!-- Source control : Need to change according to your github project -->
	<scm>
		<url>https://git.ap.manulife.com/projects/RSF</url>
		<connection>scm:git:https://git.ap.manulife.com/projects/RSF/repos/demo-order-app.git</connection>
		<developerConnection>scm:git:https://git.ap.manulife.com/projects/RSF/repos/demo-order-app.git</developerConnection>
		<tag>HEAD</tag>
	</scm>

	<!-- Properties -->
	<properties>
		<java.version>21</java.version>

		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<team.name>IFP_NB_CLAIMS</team.name>
		<team.your.name>NBUW Pillar</team.your.name>
		<team.your.email><EMAIL></team.your.email>
		<team.url>https://git.ap.manulife.com</team.url>
		<team.email><EMAIL></team.email>
		<team.zone>HKT</team.zone>
		<start-class>com.manulife.hk.Application</start-class>
		<maven-surefire-plugin.version>3.0.0-M7</maven-surefire-plugin.version>

		<!-- For RSF compliance -->
		<!-- You should use the right values for your projects -->
		<app-ci-number>CI01234567</app-ci-number>

		<swagger-codegen-maven-plugin.version>2.4.20</swagger-codegen-maven-plugin.version>
	</properties>

	<!-- TODO: Why is this section here and not in docker build on Jenkins. -->
	<distributionManagement>
		<repository>
			<id>releases</id>
			<url>https://artifactory.ap.manulife.com/artifactory/libs-release-local</url>
		</repository>
		<snapshotRepository>
			<id>snapshots</id>
			<url>https://artifactory.ap.manulife.com/artifactory/libs-snapshot-local</url>
		</snapshotRepository>
	</distributionManagement>

	<!-- Identifies parent RSF pom -->
	<parent>
		<groupId>com.manulife.ap</groupId>
		<artifactId>rsf-parent</artifactId>
		<version>2.4.1</version>
		<relativePath />
	</parent>

	<repositories>
		<repository>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
			<id>central</id>
			<name>libs-release-local</name>
			<url>https://artifactory.ap.manulife.com/artifactory/libs-release/</url>
		</repository>
		<repository>
			<snapshots />
			<id>snapshots</id>
			<name>libs-snapshot-local</name>
			<url>https://artifactory.ap.manulife.com/artifactory/libs-snapshot/</url>
		</repository>
	</repositories>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.manulife.ap</groupId>
				<artifactId>rsf-config-client-dependencies</artifactId>
				<version>2.4.1</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<!-- Project dependencies -->
	<dependencies>
		<!-- RSF CORE -->
		<dependency>
			<groupId>com.manulife.ap</groupId>
			<artifactId>rsf-core</artifactId>
			<version>2.4.1</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.manulife.ap</groupId>
			<artifactId>rsf-config-client-azure</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.17.0</version>
		</dependency>

		<dependency>
			<groupId>com.github.tomakehurst</groupId>
			<artifactId>wiremock-jre8-standalone</artifactId>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
        </dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.10.0</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.25</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15on</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk18on</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-hystrix-dashboard</artifactId>
			<version>2.2.10.RELEASE</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.25</version>
		</dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.11.0</version>
		</dependency>
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.17.2</version>
		</dependency>
		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-spring</artifactId>
			<version>4.15.1</version>
		</dependency>

		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-provider-jdbc-template</artifactId>
			<version>4.15.1</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>nimbus-jose-jwt</artifactId>
					<groupId>com.nimbusds</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-security-config</artifactId>
					<groupId>org.springframework.security</groupId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>2.0.23</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>transmittable-thread-local</artifactId>
			<version>2.14.3</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-tracing</artifactId>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-tracing-bridge-brave</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>javax.annotation</groupId>
			<artifactId>javax.annotation-api</artifactId>
			<version>1.3.2</version>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-actuator-autoconfigure</artifactId>
		</dependency>


		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>

		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
	</dependencies>

	<!-- Build plugins -->
	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<addResources>true</addResources>
					<mainClass>${start-class}</mainClass>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>${maven-surefire-plugin.version}</version>
				<configuration>
					<!-- increase test coverage -->
					<includes>
						<include>**/*IT</include>
						<include>**/*Test</include>
					</includes>
				</configuration>
			</plugin>
<!--			<plugin>-->
<!--				<groupId>org.openapitools</groupId>-->
<!--				<artifactId>openapi-generator-maven-plugin</artifactId>-->
<!--				<version>7.5.0</version>-->
<!--				<executions>-->
<!--					<execution>-->
<!--						<goals>-->
<!--							<goal>generate</goal>-->
<!--						</goals>-->
<!--						<configuration>-->
<!--							<inputSpec>-->
<!--								${project.basedir}/src/main/resources/swagger/swagger-input.yml-->
<!--							</inputSpec>-->
<!--							&lt;!&ndash;  if yaml file is not generated, use the below option and this will download the yaml file-->
<!--                            <apiDocsUrl>http://localhost:${http.port}/v3/api-docs.yaml</apiDocsUrl> &ndash;&gt;-->
<!--							<generatorName>spring</generatorName>-->
<!--							<apiPackage>com.manulife.ap.api</apiPackage>-->
<!--							<modelPackage>com.manulife.ap.model</modelPackage>-->

<!--							<configOptions>-->
<!--								<useJakartaEe>true</useJakartaEe>-->
<!--								<interfaceOnly>true</interfaceOnly>-->
<!--							</configOptions>-->
<!--						</configuration>-->
<!--					</execution>-->
<!--				</executions>-->
<!--			</plugin>-->
		</plugins>
	</build>
	<!-- Profiles -->
	<profiles>
		<profile>
			<id>dev</id>
			<properties>
				<activatedProperties>dev</activatedProperties>
				<!-- Add your Sonar authentication key. If not needed keep line commented -->
				<!-- Use authentication key. -->
				<!-- <sonar.login></sonar.login> -->
				<!-- Provide link to your documentation page for project. -->
				<sonar.host.url>https://sonar.ap.manulife.com/</sonar.host.url>
				<sonar.links.homepage>${project.url}</sonar.links.homepage>
				<sonar.language>java</sonar.language>
				<sonar.links.scm>${project.scm.url}</sonar.links.scm>
				<sonar.exclusions>**/dto/*.java,**/YamlTestConfig.java,**/MessageScheduleConfig.java</sonar.exclusions>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
	</profiles>

	<!-- Organization: Add your organization details here -->
	<organization>
		<name>${team.name}</name>
		<url>${team.url}</url>
	</organization>

	<!-- Developers: Change to your team name, id and emails. etc... -->
	<developers>
		<developer>
			<id>your-name-id</id>
			<name>${team.your.name}</name>
			<email>${team.your.email}</email>
			<roles>
				<role>developer</role>
			</roles>
			<timezone>${team.zone}</timezone>
		</developer>
	</developers>
	<!-- Contributors -->
	<contributors>
		<contributor>
			<name>${team.your.name}-id</name>
			<email>${team.your.email}</email>
			<url>${project.url}</url>
			<roles>
				<role>architect</role>
				<role>developer</role>
			</roles>
			<timezone>${team.zone}</timezone>
		</contributor>
	</contributors>
</project>