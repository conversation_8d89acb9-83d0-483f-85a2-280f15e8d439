# 数据库设计文档

## 概述

本文档描述了基于UML类图设计的数据库表结构。该设计采用了JSON存储模式，将业务数据存储在JSON_CONTEXT字段中，同时保持了清晰的关系结构。

## 设计原则

### 1. 继承关系处理
- **Table Per Class (TPC)**: 每个类都有对应的表
- **继承自CaseBaseModel的表**: 主键为`caseNo`，通过外键引用父表
- **其他继承关系**: 子表通过外键引用父表的主键

### 2. 数据存储策略
- **结构化数据**: 主键、外键、审计字段存储在独立列中
- **业务数据**: 存储在`JSON_CONTEXT`字段中，提供灵活性
- **审计字段**: 所有表都包含`create_date`、`last_update_date`、`create_by`、`update_by`

### 3. 关系映射
- **组合关系**: 通过外键实现，子实体依赖于父实体
- **聚合关系**: 通过关联表实现多对多关系
- **继承关系**: 通过外键引用父表主键

## 表结构分类

### 案件类型表 (继承CaseBaseModel)
所有案件类型表都继承自`CaseBaseModel`，主键为`caseNo`：

1. **CaseBaseModel** - 案件基础模型 (父表)
2. **PUIinquiryCase** - 保单信息查询案件
3. **NBInquiryCase** - 新业务查询案件
4. **NewBusinessCase** - 新业务案件
5. **PolicyChangeCase** - 保单变更案件
6. **ReconsiderationCase** - 复议案件
7. **NondisclosureCase** - 未披露信息案件
8. **TBC** - 待明确案件类型
9. **UnderwritingCase** - 核保案件

### 合作伙伴类型表 (继承UnderwritingPartner)
1. **UnderwritingPartner** - 核保合作伙伴 (父表)
2. **Hospital** - 医院
3. **InsuranceCompany** - 保险公司
4. **Doctor** - 医生

### 待处理事项表 (继承PendingMemo)
1. **PendingMemo** - 待处理备忘 (父表)
2. **PendingDocument** - 待处理文件
3. **F856** - 特定待办事项

### 地址相关表 (继承Place)
1. **Place** - 地点 (父表)
2. **Address** - 地址

### 独立实体表
1. **Phone** - 电话
2. **MedicalCharge** - 医疗费用
3. **DoctorBill** - 医生账单
4. **LetterTemplate** - 信函模板
5. **APLetter** - 附加信函
6. **CaseComment** - 案件评论
7. **ReferralCase** - 转介案件
8. **UserSimple** - 用户简单信息
9. **Customer** - 客户
10. **VHISLob** - 自愿医保产品线
11. **HospSubStandard** - 医院次标准
12. **FacultativeReason** - 临分原因
13. **ExtraPremiumCoverage** - 额外保费覆盖范围
14. **CounterOffer** - 反建议

### 关联表 (多对多关系)
1. **DoctorMedicalCharge** - 医生与医疗费用关联
2. **CustomerAddress** - 客户与地址关联
3. **ExtraPremiumCoverageHospSubStd** - 额外保费覆盖范围与医院次标准关联
4. **ExtraPremiumCoverageFacReason** - 额外保费覆盖范围与临分原因关联
5. **CounterOfferExtraPremiumCoverage** - 反建议与额外保费覆盖范围关联

## 关键关系说明

### 1. 核保案件的组合关系
`UnderwritingCase`通过组合关系包含：
- `CaseComment` - 案件评论列表
- `PendingMemo` - 待处理备忘录列表
- `APLetter` - 附加信函列表
- `ReferralCase` - 转介案件列表

### 2. 合作伙伴的聚合关系
`UnderwritingPartner`聚合：
- `Phone` - 业务电话和传真电话
- 通过继承扩展为`Hospital`、`Doctor`、`InsuranceCompany`

### 3. 医疗业务流程
- `Doctor`聚合`MedicalCharge`列表
- `Doctor`创建`DoctorBill`
- `DoctorBill`关联到`InsuranceCompany`
- `PendingMemo`可以引用`DoctorBill`

### 4. 信函处理流程
- `APLetter`基于`LetterTemplate`创建
- `APLetter`的收件人是`UnderwritingPartner`
- `APLetter`属于特定的`UnderwritingCase`

### 5. 反建议和保费结构
- `CounterOffer`聚合`ExtraPremiumCoverage`列表
- `ExtraPremiumCoverage`关联`VHISLob`
- `ExtraPremiumCoverage`聚合`HospSubStandard`和`FacultativeReason`列表

## JSON_CONTEXT字段内容建议

每个表的`JSON_CONTEXT`字段应包含对应UML类的所有业务属性：

### CaseBaseModel
```json
{
  "caseType": "string",
  "policyNo": "string"
}
```

### UnderwritingCase
```json
{
  "uwDecision": "string",
  "uwDecisionBy": "string",
  "uwDecisionDate": "datetime"
}
```

### UnderwritingPartner
```json
{
  "name": "string",
  "remark": "string",
  "status": "string",
  "addAddressInfo": "string"
}
```

### Phone
```json
{
  "phoneType": "string",
  "countryCode": "string",
  "areaCode": "string",
  "telNo": "string",
  "extension": "string"
}
```

## 索引建议

### 主要索引
1. 所有主键自动创建聚集索引
2. 所有外键创建非聚集索引
3. 审计字段索引：
   - `create_date`
   - `last_update_date`

### 业务索引
1. `CaseBaseModel.caseNo` - 案件编号查询
2. `UnderwritingPartner.name` - 合作伙伴名称查询
3. `Customer.user_id` - 用户关联查询
4. `APLetter.case_no` - 案件信函查询

## 数据完整性约束

### 外键约束
- 所有继承关系通过外键约束保证数据完整性
- 关联表的复合主键保证唯一性
- 可选关系使用可空外键

### 检查约束
建议在应用层实现以下业务规则：
1. 案件状态转换规则
2. 日期逻辑检查（创建日期 <= 更新日期）
3. JSON格式验证

## 扩展性考虑

### 1. 新增案件类型
- 创建新表继承`CaseBaseModel`
- 添加对应的外键约束

### 2. 新增合作伙伴类型
- 创建新表继承`UnderwritingPartner`
- 更新相关业务逻辑

### 3. JSON字段扩展
- 可以随时在JSON_CONTEXT中添加新字段
- 保持向后兼容性

## 性能优化建议

### 1. 分区策略
- 按日期分区大表（如`CaseBaseModel`）
- 按案件类型分区相关表

### 2. 查询优化
- 使用JSON索引优化JSON字段查询
- 创建复合索引支持常用查询模式

### 3. 归档策略
- 定期归档历史案件数据
- 保留必要的审计信息
