-------------------------------------------------------------------------------------------
-- Mod. Date : 28 Mar 2024
-- Mod. By   : (MITDC CD) <PERSON><PERSON><PERSON>
-- Mod. ref  : IFP_24_RL02_NBUW_18095
-- Mod. Desc : Patch data after fix wrong Chinese CDM address line order
------------------------------------------------------------------------------------------
UPDATE NB_OWNER set json_context = replace (json_context,
SUBSTRING(JSON_CONTEXT ,charindex('corrAddr"', JSON_CONTEXT) + 11, charindex('addrLine4', JSON_CONTEXT, charindex('corrAddr"', JSON_CONTEXT)) - charindex('corrAddr"', JSO<PERSON>_CONTEXT) - 12),
SUBSTRING(JSON_CONTEXT ,charindex('resAddr"', JSON_CONTEXT) + 10, charindex('addrLine4', JSON_CONTEXT) - charindex('resAddr"', JSON_CONTEXT) - 11)
)
where POLICY_NO in (
'3190186993',
'3824595403',
'3824663367',
'3824709350',
'3824712974',
'3824750669',
'3824757300',
'3824790301',
'3824792117',
'3824808921',
'3824819845',
'3824821270',
'3824822138'
);

UPDATE NB_OWNER set json_context = replace (json_context, '"sameAsResAddrInd":"N"', '"sameAsResAddrInd":"Y"'),
LAST_UPDATED_TIMESTAMP = GETDATE()
where POLICY_NO in (
'3190186993',
'3824595403',
'3824663367',
'3824709350',
'3824712974',
'3824750669',
'3824757300',
'3824790301',
'3824792117',
'3824808921',
'3824819845',
'3824821270',
'3824822138'
);

commit;