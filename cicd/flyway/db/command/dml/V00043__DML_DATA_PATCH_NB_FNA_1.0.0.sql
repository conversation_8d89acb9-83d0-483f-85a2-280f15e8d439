----------------------------------------------------------------------------------
-- Mod. Date : 08 Oct 2024
-- Mod. By   : (MITDC CD) <PERSON><PERSON><PERSON>
-- Mod. Ref  : IFP_24_RL05_NBUW_29277
-- Mod. Desc : Patch data after fix wrong RPQ data for Macau non-ILAS product
----------------------------------------------------------------------------------

update NB_FNA set 
JSON_CONTEXT = replace(JSON_CONTEXT, '"calculatedRpqScore":0,"rpqScore":null,"highestInvesRiskLevel":null,"calculatedRPQRiskLevel":0'
                                   , '"calculatedRpqScore":null,"rpqScore":null,"highestInvesRiskLevel":null,"calculatedRPQRiskLevel":null')
, LAST_UPDATED_TIMESTAMP = getdate()
where 1 = 1
and POLICY_NO in (
  select POLICY_NO from NB_FNA nf
  where 1 = 1
  and nf.POLICY_NO like '319%'
  and JSON_VALUE(JSON_QUERY(nf.JSON_CONTEXT,'$.rpqForm'), '$.rpqScore') is null
  and JSON_VALUE(JSON_QUERY(nf.JSON_CONTEXT,'$.rpqForm'), '$.calculatedRpqScore') = 0
  and JSON_VALUE(JSON_QUERY(nf.JSON_CONTEXT,'$.rpqForm'), '$.calculatedRPQRiskLevel') = 0
  and exists (
    select 1 from NB_ADMIN na
    where JSON_VALUE(na.JSON_CONTEXT, '$.stpInd') = 'Y'
    and JSON_VALUE(na.JSON_CONTEXT, '$.isFlwUpStpCase') = 'N'
    and na.POLICY_NO = nf.POLICY_NO 
  )
  and exists (
    select 1 from NB_APP_INFO nai
    where JSON_VALUE(nai.JSON_CONTEXT, '$.polStatus') = 'P'
    and nai.POLICY_NO = nf.POLICY_NO 
  )
)
;

commit;