------------------------------------------------------------------------------------------
-- Mod. Date : 02 Feb 2020
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : Initial Development
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[AssociationValueEntry](
	[id] [numeric](19, 0) NOT NULL,
	[associationKey] [nvarchar](255) NOT NULL,
	[associationValue] [nvarchar](255) NULL,
	[sagaId] [nvarchar](255) NOT NULL,
	[sagaType] [nvarchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[id] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UK_AssociationValueEntry_1] UNIQUE NONCLUSTERED 
(
	[associationKey] ASC,
	[associationValue] ASC,
	[sagaType] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UK_AssociationValueEntry_2] UNIQUE NONCLUSTERED 
(
	[sagaId] ASC,
	[sagaType] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO