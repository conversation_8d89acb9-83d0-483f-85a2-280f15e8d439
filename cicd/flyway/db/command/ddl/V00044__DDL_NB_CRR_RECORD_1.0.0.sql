------------------------------------------------------------------------------------------
-- Mod. Date : 05 Sep 2024
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_25_RL01_CRR_1017
-- Mod. Desc : CRR
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[NB_CRR_RECORD](
	[POLICY_NO] [NVARCHAR](20) NOT NULL,
	[NB_NBTS_DEL_STA] [NVARCHAR](5) NOT NULL,
	[PHI_DEL_STA] [NVARCHAR](5) NOT NULL,
	[FNA_DEL_STA] [NVARCHAR](5) NOT NULL,
	[REPORT_DEL_STA] [NVARCHAR](5) NOT NULL,
	[SUBMISSION_DEL_STA] [NVARCHAR](5) NOT NULL,
	[NBCORE_DEL_STA] [NVARCHAR](5) NOT NULL,
	[EXCLUSION_DEL_STA] [NVARCHAR](5) NOT NULL,
	[CREATED_TIMESTAMP] [DATETIME] NOT NULL,
	[CREATED_BY] [VARCHAR](50) NOT NULL,
	[LAST_UPDATED_TIMESTAMP] [DATETIME] NULL,
	[LAST_UPDATED_BY] [VARCHAR](50) NULL,
	[DELETED_BY] [VARCHAR](50) NULL,
 CONSTRAINT [PK_NB_CRR_RECORD] PRIMARY KEY CLUSTERED
(
    [POLICY_NO] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE INDEX [INDEX_NB_CRR_RECORD_LAST_UPDATED_TIMESTAMP] ON [DBO].[NB_CRR_RECORD] ([LAST_UPDATED_TIMESTAMP])
GO