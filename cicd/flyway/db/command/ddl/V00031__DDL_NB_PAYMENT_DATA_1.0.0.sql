------------------------------------------------------------------------------------------
-- Mod. Date : 09 Feb 2022
-- Mod. By   : (MITDC CD) <PERSON> HU
-- Mod. ref  : IFP_22_RL04_JMIFUM_389
-- Mod. Desc : [DEV] Saving Payment info in NBUWS during policy issue
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[NB_PAYMENT_DATA](
	[POLICY_NO] [NVARCHAR](20) NOT NULL,
    [JSON_CONTEXT] [NVA<PERSON>HA<PERSON>](MAX) NOT NULL,
    [CREATED_TIMESTAMP] [DATETIME] NOT NULL,
    [CREATED_BY] [VARCHAR](50) NOT NULL,
    [LAST_UPDATED_TIMESTAMP] [DATETIME] NULL,
    [LAST_UPDATED_BY] [VARCHAR](50) NULL,
    [DELETED_TIMESTAMP] [DATETIME] NULL,
    [DELETED_BY] [VARCHAR](50) NULL,
 CONSTRAINT [PK_NB_PAYMENT_SUSPENSE] PRIMARY KEY CLUSTERED
(
	[POLICY_NO] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO