------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[NB_REQUEST_LOG](
	[REQ_ID] [NUMERIC](15) NOT NULL,
	[REQ_DATE] [DATETIME] NULL,
	[REQ_URL] [NVA<PERSON>HA<PERSON>](3000) NULL,
	[POLICY_NO] [NVARCHA<PERSON>](20) NULL,
	[STATUS] [NUMERIC](3, 0) NULL,
	[REQ_BODY] [NVARCHAR](MAX) NULL,
	[RES_BODY] [NVARCHAR](MAX) NULL,
 CONSTRAINT [PK_NB_REQUEST_LOG] PRIMARY KEY CLUSTERED 
(
	[REQ_ID] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 INDEX [IDX_nb_req_log_pol] NONCLUSTERED 
 (
	[POLICY_NO] ASC,
	[REQ_DATE] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE SEQUENCE [dbo].[SEQ_NB_REQUEST_LOG] AS [NUMERIC](10) START WITH 1 INCREMENT BY 1 CACHE 50
GO