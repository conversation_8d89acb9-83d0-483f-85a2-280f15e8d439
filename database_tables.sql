-- 数据库表结构定义
-- 基于UML类图描述生成的表结构

-- 1. 案件基础模型表 (所有案件类型的父表)
CREATE TABLE CaseBaseModel (
    caseNo NVARCHAR(20) NOT NULL CONSTRAINT PK_CaseBaseModel PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL
);

-- 2. 保单信息查询案件
CREATE TABLE PUIinquiryCase (
    caseNo NVARCHAR(20) NOT NULL CONSTRAINT PK_PUIinquiryCase PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHA<PERSON>(50) NOT NULL,
    CONSTRAINT FK_PUIinquiryCase_CaseBaseModel FOREIGN KEY (caseNo) REFERENCES CaseBaseModel(caseNo)
);

-- 3. 新业务查询案件
CREATE TABLE NBInquiryCase (
    caseNo NVARCHAR(20) NOT NULL CONSTRAINT PK_NBInquiryCase PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_NBInquiryCase_CaseBaseModel FOREIGN KEY (caseNo) REFERENCES CaseBaseModel(caseNo)
);

-- 4. 新业务案件
CREATE TABLE NewBusinessCase (
    caseNo NVARCHAR(20) NOT NULL CONSTRAINT PK_NewBusinessCase PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_NewBusinessCase_CaseBaseModel FOREIGN KEY (caseNo) REFERENCES CaseBaseModel(caseNo)
);

-- 5. 保单变更案件
CREATE TABLE PolicyChangeCase (
    caseNo NVARCHAR(20) NOT NULL CONSTRAINT PK_PolicyChangeCase PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_PolicyChangeCase_CaseBaseModel FOREIGN KEY (caseNo) REFERENCES CaseBaseModel(caseNo)
);

-- 6. 复议案件
CREATE TABLE ReconsiderationCase (
    caseNo NVARCHAR(20) NOT NULL CONSTRAINT PK_ReconsiderationCase PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_ReconsiderationCase_CaseBaseModel FOREIGN KEY (caseNo) REFERENCES CaseBaseModel(caseNo)
);

-- 7. 未披露信息案件
CREATE TABLE NondisclosureCase (
    caseNo NVARCHAR(20) NOT NULL CONSTRAINT PK_NondisclosureCase PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_NondisclosureCase_CaseBaseModel FOREIGN KEY (caseNo) REFERENCES CaseBaseModel(caseNo)
);

-- 8. 待明确案件类型
CREATE TABLE TBC (
    caseNo NVARCHAR(20) NOT NULL CONSTRAINT PK_TBC PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_TBC_CaseBaseModel FOREIGN KEY (caseNo) REFERENCES CaseBaseModel(caseNo)
);

-- 9. 核保案件
CREATE TABLE UnderwritingCase (
    caseNo NVARCHAR(20) NOT NULL CONSTRAINT PK_UnderwritingCase PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_UnderwritingCase_CaseBaseModel FOREIGN KEY (caseNo) REFERENCES CaseBaseModel(caseNo)
);

-- 10. 地点表
CREATE TABLE Place (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_Place PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL
);

-- 11. 地址表 (继承Place)
CREATE TABLE Address (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_Address PRIMARY KEY,
    place_id NVARCHAR(20) NOT NULL,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_Address_Place FOREIGN KEY (place_id) REFERENCES Place(id)
);

-- 12. 电话表
CREATE TABLE Phone (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_Phone PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL
);

-- 13. 核保合作伙伴表
CREATE TABLE UnderwritingPartner (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_UnderwritingPartner PRIMARY KEY,
    biz_phone_id NVARCHAR(20),
    fax_phone_id NVARCHAR(20),
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_UnderwritingPartner_BizPhone FOREIGN KEY (biz_phone_id) REFERENCES Phone(id),
    CONSTRAINT FK_UnderwritingPartner_FaxPhone FOREIGN KEY (fax_phone_id) REFERENCES Phone(id)
);

-- 14. 医院表 (继承UnderwritingPartner)
CREATE TABLE Hospital (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_Hospital PRIMARY KEY,
    partner_id NVARCHAR(20) NOT NULL,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_Hospital_UnderwritingPartner FOREIGN KEY (partner_id) REFERENCES UnderwritingPartner(id)
);

-- 15. 保险公司表 (继承UnderwritingPartner)
CREATE TABLE InsuranceCompany (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_InsuranceCompany PRIMARY KEY,
    partner_id NVARCHAR(20) NOT NULL,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_InsuranceCompany_UnderwritingPartner FOREIGN KEY (partner_id) REFERENCES UnderwritingPartner(id)
);

-- 16. 医疗费用表
CREATE TABLE MedicalCharge (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_MedicalCharge PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL
);

-- 17. 医生表 (继承UnderwritingPartner)
CREATE TABLE Doctor (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_Doctor PRIMARY KEY,
    partner_id NVARCHAR(20) NOT NULL,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_Doctor_UnderwritingPartner FOREIGN KEY (partner_id) REFERENCES UnderwritingPartner(id)
);

-- 18. 医生医疗费用关联表 (聚合关系)
CREATE TABLE DoctorMedicalCharge (
    doctor_id NVARCHAR(20) NOT NULL,
    medical_charge_id NVARCHAR(20) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT PK_DoctorMedicalCharge PRIMARY KEY (doctor_id, medical_charge_id),
    CONSTRAINT FK_DoctorMedicalCharge_Doctor FOREIGN KEY (doctor_id) REFERENCES Doctor(id),
    CONSTRAINT FK_DoctorMedicalCharge_MedicalCharge FOREIGN KEY (medical_charge_id) REFERENCES MedicalCharge(id)
);

-- 19. 医生账单表
CREATE TABLE DoctorBill (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_DoctorBill PRIMARY KEY,
    doctor_id NVARCHAR(20) NOT NULL,
    insurance_company_id NVARCHAR(20) NOT NULL,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_DoctorBill_Doctor FOREIGN KEY (doctor_id) REFERENCES Doctor(id),
    CONSTRAINT FK_DoctorBill_InsuranceCompany FOREIGN KEY (insurance_company_id) REFERENCES InsuranceCompany(id)
);

-- 20. 信函模板表
CREATE TABLE LetterTemplate (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_LetterTemplate PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL
);

-- 21. 附加信函表
CREATE TABLE APLetter (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_APLetter PRIMARY KEY,
    case_no NVARCHAR(20) NOT NULL,
    letter_recipient_id NVARCHAR(20) NOT NULL,
    letter_template_id NVARCHAR(20) NOT NULL,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_APLetter_UnderwritingCase FOREIGN KEY (case_no) REFERENCES UnderwritingCase(caseNo),
    CONSTRAINT FK_APLetter_Recipient FOREIGN KEY (letter_recipient_id) REFERENCES UnderwritingPartner(id),
    CONSTRAINT FK_APLetter_Template FOREIGN KEY (letter_template_id) REFERENCES LetterTemplate(id)
);

-- 22. 待处理备忘表 (父表)
CREATE TABLE PendingMemo (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_PendingMemo PRIMARY KEY,
    case_no NVARCHAR(20) NOT NULL,
    doctor_bill_id NVARCHAR(20),
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_PendingMemo_UnderwritingCase FOREIGN KEY (case_no) REFERENCES UnderwritingCase(caseNo),
    CONSTRAINT FK_PendingMemo_DoctorBill FOREIGN KEY (doctor_bill_id) REFERENCES DoctorBill(id)
);

-- 23. 待处理文件表 (继承PendingMemo)
CREATE TABLE PendingDocument (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_PendingDocument PRIMARY KEY,
    pending_memo_id NVARCHAR(20) NOT NULL,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_PendingDocument_PendingMemo FOREIGN KEY (pending_memo_id) REFERENCES PendingMemo(id)
);

-- 24. F856特定待办事项表 (继承PendingMemo)
CREATE TABLE F856 (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_F856 PRIMARY KEY,
    pending_memo_id NVARCHAR(20) NOT NULL,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_F856_PendingMemo FOREIGN KEY (pending_memo_id) REFERENCES PendingMemo(id)
);

-- 25. 案件评论表
CREATE TABLE CaseComment (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_CaseComment PRIMARY KEY,
    case_no NVARCHAR(20) NOT NULL,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_CaseComment_UnderwritingCase FOREIGN KEY (case_no) REFERENCES UnderwritingCase(caseNo)
);

-- 26. 转介案件表
CREATE TABLE ReferralCase (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_ReferralCase PRIMARY KEY,
    case_no NVARCHAR(20) NOT NULL,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_ReferralCase_UnderwritingCase FOREIGN KEY (case_no) REFERENCES UnderwritingCase(caseNo)
);

-- 27. 用户简单信息表
CREATE TABLE UserSimple (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_UserSimple PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL
);

-- 28. 客户表
CREATE TABLE Customer (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_Customer PRIMARY KEY,
    user_id NVARCHAR(20),
    case_no NVARCHAR(20),
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_Customer_UserSimple FOREIGN KEY (user_id) REFERENCES UserSimple(id),
    CONSTRAINT FK_Customer_UnderwritingCase FOREIGN KEY (case_no) REFERENCES UnderwritingCase(caseNo)
);

-- 29. 客户地址关联表 (聚合关系)
CREATE TABLE CustomerAddress (
    customer_id NVARCHAR(20) NOT NULL,
    address_id NVARCHAR(20) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT PK_CustomerAddress PRIMARY KEY (customer_id, address_id),
    CONSTRAINT FK_CustomerAddress_Customer FOREIGN KEY (customer_id) REFERENCES Customer(id),
    CONSTRAINT FK_CustomerAddress_Address FOREIGN KEY (address_id) REFERENCES Address(id)
);

-- 30. 自愿医保产品线表
CREATE TABLE VHISLob (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_VHISLob PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL
);

-- 31. 医院次标准表
CREATE TABLE HospSubStandard (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_HospSubStandard PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL
);

-- 32. 临分原因表
CREATE TABLE FacultativeReason (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_FacultativeReason PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL
);

-- 33. 额外保费覆盖范围表
CREATE TABLE ExtraPremiumCoverage (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_ExtraPremiumCoverage PRIMARY KEY,
    vhis_lob_id NVARCHAR(20),
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT FK_ExtraPremiumCoverage_VHISLob FOREIGN KEY (vhis_lob_id) REFERENCES VHISLob(id)
);

-- 34. 额外保费覆盖范围与医院次标准关联表
CREATE TABLE ExtraPremiumCoverageHospSubStd (
    extra_premium_coverage_id NVARCHAR(20) NOT NULL,
    hosp_sub_standard_id NVARCHAR(20) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT PK_ExtraPremiumCoverageHospSubStd PRIMARY KEY (extra_premium_coverage_id, hosp_sub_standard_id),
    CONSTRAINT FK_ExtraPremiumCoverageHospSubStd_Coverage FOREIGN KEY (extra_premium_coverage_id) REFERENCES ExtraPremiumCoverage(id),
    CONSTRAINT FK_ExtraPremiumCoverageHospSubStd_HospSubStd FOREIGN KEY (hosp_sub_standard_id) REFERENCES HospSubStandard(id)
);

-- 35. 额外保费覆盖范围与临分原因关联表
CREATE TABLE ExtraPremiumCoverageFacReason (
    extra_premium_coverage_id NVARCHAR(20) NOT NULL,
    facultative_reason_id NVARCHAR(20) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT PK_ExtraPremiumCoverageFacReason PRIMARY KEY (extra_premium_coverage_id, facultative_reason_id),
    CONSTRAINT FK_ExtraPremiumCoverageFacReason_Coverage FOREIGN KEY (extra_premium_coverage_id) REFERENCES ExtraPremiumCoverage(id),
    CONSTRAINT FK_ExtraPremiumCoverageFacReason_FacReason FOREIGN KEY (facultative_reason_id) REFERENCES FacultativeReason(id)
);

-- 36. 反建议表
CREATE TABLE CounterOffer (
    id NVARCHAR(20) NOT NULL CONSTRAINT PK_CounterOffer PRIMARY KEY,
    JSON_CONTEXT NVARCHAR(MAX) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL
);

-- 37. 反建议与额外保费覆盖范围关联表
CREATE TABLE CounterOfferExtraPremiumCoverage (
    counter_offer_id NVARCHAR(20) NOT NULL,
    extra_premium_coverage_id NVARCHAR(20) NOT NULL,
    create_date DATETIME NOT NULL,
    last_update_date DATETIME NOT NULL,
    create_by VARCHAR(50) NOT NULL,
    update_by VARCHAR(50) NOT NULL,
    CONSTRAINT PK_CounterOfferExtraPremiumCoverage PRIMARY KEY (counter_offer_id, extra_premium_coverage_id),
    CONSTRAINT FK_CounterOfferExtraPremiumCoverage_CounterOffer FOREIGN KEY (counter_offer_id) REFERENCES CounterOffer(id),
    CONSTRAINT FK_CounterOfferExtraPremiumCoverage_Coverage FOREIGN KEY (extra_premium_coverage_id) REFERENCES ExtraPremiumCoverage(id)
);
